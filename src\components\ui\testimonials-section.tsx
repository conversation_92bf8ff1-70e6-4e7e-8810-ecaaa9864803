import { Card, CardContent } from "@/components/ui/card";
import { Star } from "lucide-react";

const testimonials = [
  {
    name: "<PERSON>",
    role: "Homeowner",
    content: "A.C.A.P connected me with an amazing cleaning service. The transparency in pricing and the quality of work exceeded my expectations.",
    rating: 5
  },
  {
    name: "<PERSON>",
    role: "Plumber",
    content: "Joining A.C.A.P has transformed my business. The steady flow of clients and fair compensation make this platform exceptional.",
    rating: 5
  },
  {
    name: "<PERSON>",
    role: "Property Manager",
    content: "Managing multiple properties is easier with A.C.A.P. Reliable workers, clear pricing, and excellent customer service.",
    rating: 5
  }
];

export function TestimonialsSection() {
  return (
    <section className="py-16 bg-background">
      <div className="container px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            What Our Community Says
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Real feedback from clients and service providers who trust A.C.A.P.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="border-border/50 hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-accent text-accent" />
                  ))}
                </div>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  "{testimonial.content}"
                </p>
                <div>
                  <p className="font-semibold text-foreground">{testimonial.name}</p>
                  <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}