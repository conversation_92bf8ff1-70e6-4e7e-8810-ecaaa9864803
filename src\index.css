@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* African-inspired color palette */
    --background: 35 20% 96%;
    --foreground: 30 15% 25%;

    --card: 35 25% 98%;
    --card-foreground: 30 15% 25%;

    --popover: 35 25% 98%;
    --popover-foreground: 30 15% 25%;

    /* Warm orange primary */
    --primary: 25 85% 55%;
    --primary-foreground: 35 25% 98%;

    /* Deep green secondary */
    --secondary: 140 40% 25%;
    --secondary-foreground: 35 25% 98%;

    /* Earthy muted tones */
    --muted: 35 15% 90%;
    --muted-foreground: 30 20% 45%;

    /* Gold accent */
    --accent: 45 85% 65%;
    --accent-foreground: 30 15% 25%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 35 25% 98%;

    --border: 35 20% 85%;
    --input: 35 20% 92%;
    --ring: 25 85% 55%;

    /* African patterns and gradients */
    --gradient-hero: linear-gradient(135deg, hsl(25 85% 55%) 0%, hsl(140 40% 25%) 100%);
    --gradient-section: linear-gradient(180deg, hsl(35 20% 98%) 0%, hsl(35 15% 95%) 100%);
    --pattern-subtle: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23F5C842' fill-opacity='0.03'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm10 0c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 30 25% 8%;
    --foreground: 35 25% 95%;

    --card: 30 25% 10%;
    --card-foreground: 35 25% 95%;

    --popover: 30 25% 10%;
    --popover-foreground: 35 25% 95%;

    --primary: 25 75% 50%;
    --primary-foreground: 30 25% 8%;

    --secondary: 140 35% 20%;
    --secondary-foreground: 35 25% 95%;

    --muted: 30 20% 15%;
    --muted-foreground: 35 15% 65%;

    --accent: 45 75% 60%;
    --accent-foreground: 30 25% 8%;

    --destructive: 0 65% 45%;
    --destructive-foreground: 35 25% 95%;

    --border: 30 20% 20%;
    --input: 30 20% 18%;
    --ring: 25 75% 50%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }
}

@layer utilities {
  .hero-gradient {
    background: var(--gradient-hero);
  }
  
  .section-gradient {
    background: var(--gradient-section);
  }
  
  .pattern-bg {
    background-image: var(--pattern-subtle);
  }
  
  .african-pattern {
    position: relative;
  }
  
  .african-pattern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: var(--pattern-subtle);
    opacity: 0.3;
    pointer-events: none;
  }
}